import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/middleware'

// GET user profile
export const GET = withAuth(async (request: NextRequest, user: any) => {
  try {
    const userProfile = await prisma.user.findUnique({
      where: { id: user.userId },
      select: {
        id: true,
        email: true,
        username: true,
        openaiApiKey: true,
        preferredModel: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    if (!userProfile) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      user: userProfile,
    })
  } catch (error) {
    console.error('Get profile error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
})

// PUT update user profile
export const PUT = withAuth(async (request: NextRequest, user: any) => {
  try {
    const body = await request.json()
    const { openaiApiKey, preferredModel } = body

    const updatedUser = await prisma.user.update({
      where: { id: user.userId },
      data: {
        ...(openaiApiKey !== undefined && { openaiApiKey }),
        ...(preferredModel !== undefined && { preferredModel }),
      },
      select: {
        id: true,
        email: true,
        username: true,
        openaiApiKey: true,
        preferredModel: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    return NextResponse.json({
      success: true,
      user: updatedUser,
    })
  } catch (error) {
    console.error('Update profile error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
})
