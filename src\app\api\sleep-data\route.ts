import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/middleware'
import { SleepDataInput } from '@/types'

// GET sleep data for the user
export const GET = withAuth(async (request: NextRequest, user: any) => {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')
    
    const sleepData = await prisma.sleepData.findMany({
      where: { userId: user.userId },
      orderBy: { date: 'desc' },
      take: days,
    })

    return NextResponse.json({
      success: true,
      data: sleepData,
    })
  } catch (error) {
    console.error('Get sleep data error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
})

// POST new sleep data
export const POST = withAuth(async (request: NextRequest, user: any) => {
  try {
    const body: SleepDataInput = await request.json()
    const {
      sleep_total,
      sleep_rem,
      sleep_deep,
      had_late_meal,
      read_before_bed,
      used_screen_before_bed,
      date
    } = body

    // Validate required fields
    if (!sleep_total || !date) {
      return NextResponse.json(
        { success: false, message: 'Sleep total and date are required' },
        { status: 400 }
      )
    }

    // Check if data for this date already exists
    const existingData = await prisma.sleepData.findUnique({
      where: {
        userId_date: {
          userId: user.userId,
          date: BigInt(date)
        }
      }
    })

    if (existingData) {
      // Update existing data
      const updatedData = await prisma.sleepData.update({
        where: {
          userId_date: {
            userId: user.userId,
            date: BigInt(date)
          }
        },
        data: {
          sleepTotal: sleep_total,
          sleepRem: sleep_rem || null,
          sleepDeep: sleep_deep || null,
          hadLateMeal: had_late_meal,
          readBeforeBed: read_before_bed,
          usedScreenBeforeBed: used_screen_before_bed,
        }
      })

      return NextResponse.json({
        success: true,
        data: updatedData,
        message: 'Sleep data updated successfully'
      })
    } else {
      // Create new data
      const newData = await prisma.sleepData.create({
        data: {
          userId: user.userId,
          sleepTotal: sleep_total,
          sleepRem: sleep_rem || null,
          sleepDeep: sleep_deep || null,
          hadLateMeal: had_late_meal,
          readBeforeBed: read_before_bed,
          usedScreenBeforeBed: used_screen_before_bed,
          date: BigInt(date),
        }
      })

      return NextResponse.json({
        success: true,
        data: newData,
        message: 'Sleep data created successfully'
      })
    }
  } catch (error) {
    console.error('Post sleep data error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
})
