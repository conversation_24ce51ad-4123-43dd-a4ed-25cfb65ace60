import { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  SleepDataInput,
  ChatRequest,
  ChatResponse,
  RecommendationResponse
} from '@/types'

class ApiClient {
  private baseUrl: string
  private token: string | null = null

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || ''
  }

  setToken(token: string | null) {
    this.token = token
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}/api${endpoint}`
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Auth endpoints
  async login(data: LoginRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async register(data: RegisterRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // User endpoints
  async getUserProfile() {
    return this.request('/user/profile')
  }

  async updateUserProfile(data: { openaiApiKey?: string; preferredModel?: string }) {
    return this.request('/user/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  // Sleep data endpoints
  async getSleepData(days: number = 7) {
    return this.request(`/sleep-data?days=${days}`)
  }

  async addSleepData(data: SleepDataInput) {
    return this.request('/sleep-data', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // Data processing
  async processData(formData: FormData) {
    return this.request('/process-data', {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set Content-Type for FormData
    })
  }

  // Recommendations
  async getRecommendations(): Promise<RecommendationResponse> {
    return this.request<RecommendationResponse>('/recommendations', {
      method: 'POST',
    })
  }

  // Chat endpoints
  async getChatHistory(limit: number = 50) {
    return this.request(`/chat?limit=${limit}`)
  }

  async sendChatMessage(data: ChatRequest): Promise<ChatResponse> {
    return this.request<ChatResponse>('/chat', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }
}

export const apiClient = new ApiClient()
