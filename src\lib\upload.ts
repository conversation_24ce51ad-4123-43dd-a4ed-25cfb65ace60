import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const UPLOAD_DIR = process.env.UPLOAD_DIR || './uploads'
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '10485760') // 10MB

export async function saveUploadedFile(
  file: File,
  userId: number,
  fileType: 'image' | 'audio'
): Promise<{ filename: string; filePath: string }> {
  // Validate file size
  if (file.size > MAX_FILE_SIZE) {
    throw new Error(`File size exceeds maximum limit of ${MAX_FILE_SIZE / 1024 / 1024}MB`)
  }

  // Validate file type
  const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  const allowedAudioTypes = ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/m4a', 'audio/webm']
  
  if (fileType === 'image' && !allowedImageTypes.includes(file.type)) {
    throw new Error('Invalid image file type. Allowed: JPEG, PNG, WebP')
  }
  
  if (fileType === 'audio' && !allowedAudioTypes.includes(file.type)) {
    throw new Error('Invalid audio file type. Allowed: MP3, WAV, M4A, WebM')
  }

  // Create upload directory if it doesn't exist
  const userUploadDir = path.join(UPLOAD_DIR, `user_${userId}`, fileType)
  if (!existsSync(userUploadDir)) {
    await mkdir(userUploadDir, { recursive: true })
  }

  // Generate unique filename
  const timestamp = Date.now()
  const extension = path.extname(file.name)
  const filename = `${timestamp}_${Math.random().toString(36).substring(2)}${extension}`
  const filePath = path.join(userUploadDir, filename)

  // Save file
  const bytes = await file.arrayBuffer()
  const buffer = Buffer.from(bytes)
  await writeFile(filePath, buffer)

  return {
    filename,
    filePath: filePath.replace(/\\/g, '/'), // Normalize path separators
  }
}
