# Sleep Pattern App – Product Specification

## App Overview
A sleep tracking and coaching application that allows users to upload sleep data via screenshots, record voice memos about their sleep habits, visualize patterns through charts, and receive personalized recommendations and interactive coaching from an AI-powered sleep coach.  

---

## Pages & Navigation

### Main Navigation
- **Home Page** – Primary dashboard with data entry, analytics, and recommendations  
- **Sleep Coach Page** – Interactive AI chatbot interface for personalized guidance  
- **Login Page** – User authentication  
- **Registration Page** – New user account creation  
- **Account Page** – User settings and API configuration  

---

## Page Specifications

### Home Page
- Button **“Enter data for today”**  
  - Dropdown with:  
    - **Upload sleep data screenshot** (file picker)  
    - **Record a memo** (voice recording + stop + submit)  
- **Submit** uploads screenshot + audio → backend → GPT-4o → structured JSON saved in DB  
- **Give me recommendations** button  
  - Sends all user sleep data to GPT-4o for personalized insights  
  - Shows AI-generated recommendations (with loading state)  
- Data visualizations:  
  - **Bar chart “Sleep” (last 7 days)**  
    - Hours slept, REM, deep sleep (color-segmented bars)  
  - **Bar chart “Sleep hygiene” (last 7 days)**  
    - Boolean activities: “Had late meal”, “Read before bed”, “Used screen before bed”  

---

### Sleep Coach Page
- Chat interface with:  
  - Message history (user + AI coach)  
  - Input field and **Send** button  
- Backend sends conversation history + user’s sleep data as **system prompt** to GPT-4o  
- Supports ongoing personalized Q&A about habits, progress, and improvements  

---

### Login Page
- Email/username input field  
- Password input field  
- **Login** button  
- **Forgot Password?** link  
- **Don’t have an account? Register** link  

---

### Registration Page
- Email, username, and password fields  
- **Register** button  
- Link to **Login** page  

---

### Account Page
- Field for storing user’s **OpenAI API key** (encrypted)  
- Dropdown for selecting preferred **LLM model**  
- Save/Update settings  

---

## Data Specification

### Expected JSON Output
```json
[{
  "sleep_total": <number_of_seconds>,
  "sleep_rem": <number_of_seconds>,
  "sleep_deep": <number_of_seconds>,
  "had_late_meal": <true_or_false>,
  "read_before_bed": <true_or_false>,
  "used_screen_before_bed": <true_or_false>,
  "date": <date_in_ms>
}]
```

---

## Core AI Prompts
- **Data extraction prompt**: Include today’s date so the model can resolve relative references (“yesterday”, “two days ago”). The model should output an **array of objects**, one per referenced day.  
- **Recommendations prompt**: *“Please take a look at my sleep patterns and sleep hygiene, analyse what works best for me to get a better sleep and tell me recommendations for what should I do to improve my sleep. Also, refer to the sleep data that I sent you so I know what your recommendation is based on.”*  
- **Chatbot system message**: Provide all historical sleep data + ongoing conversation for personalized, context-aware coaching.  
