import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/middleware'
import { chatWithSleepCoach } from '@/lib/openai'
import { ChatRequest, ChatResponse } from '@/types'

// GET chat history
export const GET = withAuth(async (request: NextRequest, user: any) => {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    
    const chatHistory = await prisma.chatHistory.findMany({
      where: { userId: user.userId },
      orderBy: { createdAt: 'asc' },
      take: limit,
    })

    return NextResponse.json({
      success: true,
      data: chatHistory,
    })
  } catch (error) {
    console.error('Get chat history error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
})

// POST new chat message
export const POST = withAuth(async (request: NextRequest, user: any) => {
  try {
    const body: ChatRequest = await request.json()
    const { message } = body

    if (!message || message.trim().length === 0) {
      return NextResponse.json<ChatResponse>(
        { success: false, message: 'Message is required' },
        { status: 400 }
      )
    }

    // Get user's OpenAI API key
    const userProfile = await prisma.user.findUnique({
      where: { id: user.userId },
      select: { openaiApiKey: true }
    })

    if (!userProfile?.openaiApiKey) {
      return NextResponse.json<ChatResponse>(
        { success: false, message: 'OpenAI API key not configured. Please set it in your account settings.' },
        { status: 400 }
      )
    }

    // Get recent chat history
    const recentChatHistory = await prisma.chatHistory.findMany({
      where: { userId: user.userId },
      orderBy: { createdAt: 'desc' },
      take: 20,
      select: {
        message: true,
        isUserMessage: true,
      }
    })

    // Reverse to get chronological order
    const chatHistory = recentChatHistory.reverse()

    // Get user's recent sleep data
    const sleepData = await prisma.sleepData.findMany({
      where: { userId: user.userId },
      orderBy: { date: 'desc' },
      take: 7,
    })

    // Save user message to database
    await prisma.chatHistory.create({
      data: {
        userId: user.userId,
        message: message.trim(),
        isUserMessage: true,
      }
    })

    // Get AI response
    const aiResponse = await chatWithSleepCoach(
      message.trim(),
      chatHistory,
      sleepData,
      userProfile.openaiApiKey
    )

    // Save AI response to database
    await prisma.chatHistory.create({
      data: {
        userId: user.userId,
        message: aiResponse,
        isUserMessage: false,
      }
    })

    return NextResponse.json<ChatResponse>({
      success: true,
      response: aiResponse,
    })

  } catch (error) {
    console.error('Chat error:', error)
    
    if (error instanceof Error && error.message.includes('OpenAI')) {
      return NextResponse.json<ChatResponse>(
        { success: false, message: 'Failed to get response from sleep coach. Please check your OpenAI API key.' },
        { status: 400 }
      )
    }

    return NextResponse.json<ChatResponse>(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
})
