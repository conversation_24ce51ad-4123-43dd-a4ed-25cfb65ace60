import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/middleware'
import { extractSleepDataFromText } from '@/lib/openai'

export const POST = withAuth(async (request: NextRequest, user: any) => {
  try {
    const formData = await request.formData()
    const text = formData.get('text') as string
    const file = formData.get('file') as File | null

    // Get user's OpenAI API key
    const userProfile = await prisma.user.findUnique({
      where: { id: user.userId },
      select: { openaiApiKey: true }
    })

    if (!userProfile?.openaiApiKey) {
      return NextResponse.json(
        { success: false, message: 'OpenAI API key not configured. Please set it in your account settings.' },
        { status: 400 }
      )
    }

    let textToProcess = text || ''

    // If a file is uploaded, we would process it here
    // For now, we'll focus on text processing
    if (file && !text) {
      return NextResponse.json(
        { success: false, message: 'File processing not yet implemented. Please provide text input.' },
        { status: 400 }
      )
    }

    if (!textToProcess) {
      return NextResponse.json(
        { success: false, message: 'No text provided for processing' },
        { status: 400 }
      )
    }

    // Extract sleep data using OpenAI
    const extractedData = await extractSleepDataFromText(textToProcess, userProfile.openaiApiKey)

    // Save extracted data to database
    const savedData = []
    for (const data of extractedData) {
      try {
        // Check if data for this date already exists
        const existingData = await prisma.sleepData.findUnique({
          where: {
            userId_date: {
              userId: user.userId,
              date: BigInt(data.date)
            }
          }
        })

        if (existingData) {
          // Update existing data
          const updatedData = await prisma.sleepData.update({
            where: {
              userId_date: {
                userId: user.userId,
                date: BigInt(data.date)
              }
            },
            data: {
              sleepTotal: data.sleep_total,
              sleepRem: data.sleep_rem || null,
              sleepDeep: data.sleep_deep || null,
              hadLateMeal: data.had_late_meal,
              readBeforeBed: data.read_before_bed,
              usedScreenBeforeBed: data.used_screen_before_bed,
            }
          })
          savedData.push(updatedData)
        } else {
          // Create new data
          const newData = await prisma.sleepData.create({
            data: {
              userId: user.userId,
              sleepTotal: data.sleep_total,
              sleepRem: data.sleep_rem || null,
              sleepDeep: data.sleep_deep || null,
              hadLateMeal: data.had_late_meal,
              readBeforeBed: data.read_before_bed,
              usedScreenBeforeBed: data.used_screen_before_bed,
              date: BigInt(data.date),
            }
          })
          savedData.push(newData)
        }
      } catch (dbError) {
        console.error('Error saving sleep data:', dbError)
        // Continue with other data points even if one fails
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully processed and saved ${savedData.length} sleep data entries`,
      data: savedData,
      extracted: extractedData
    })

  } catch (error) {
    console.error('Process data error:', error)
    
    if (error instanceof Error && error.message.includes('OpenAI')) {
      return NextResponse.json(
        { success: false, message: 'Failed to process data with AI. Please check your OpenAI API key.' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
})
