import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/middleware'
import { generateRecommendations } from '@/lib/openai'
import { RecommendationResponse } from '@/types'

export const POST = withAuth(async (request: NextRequest, user: any) => {
  try {
    // Get user's OpenAI API key
    const userProfile = await prisma.user.findUnique({
      where: { id: user.userId },
      select: { openaiApiKey: true }
    })

    if (!userProfile?.openaiApiKey) {
      return NextResponse.json<RecommendationResponse>(
        { success: false, message: 'OpenAI API key not configured. Please set it in your account settings.' },
        { status: 400 }
      )
    }

    // Get user's sleep data from the last 7 days
    const sleepData = await prisma.sleepData.findMany({
      where: { userId: user.userId },
      orderBy: { date: 'desc' },
      take: 7,
    })

    if (sleepData.length === 0) {
      return NextResponse.json<RecommendationResponse>(
        { success: false, message: 'No sleep data found. Please add some sleep data first.' },
        { status: 400 }
      )
    }

    // Generate recommendations using OpenAI
    const recommendations = await generateRecommendations(sleepData, userProfile.openaiApiKey)

    return NextResponse.json<RecommendationResponse>({
      success: true,
      recommendations,
    })

  } catch (error) {
    console.error('Generate recommendations error:', error)
    
    if (error instanceof Error && error.message.includes('OpenAI')) {
      return NextResponse.json<RecommendationResponse>(
        { success: false, message: 'Failed to generate recommendations. Please check your OpenAI API key.' },
        { status: 400 }
      )
    }

    return NextResponse.json<RecommendationResponse>(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
})
