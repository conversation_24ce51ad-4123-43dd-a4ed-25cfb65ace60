# Sleep Pattern App

## Overview
Sleep Pattern App is a sleep tracking and AI coaching application.  
It enables users to:  
- Upload sleep data screenshots  
- Record voice memos about their sleep habits  
- Store structured sleep data in a database  
- Visualize patterns (sleep duration, REM, deep sleep, sleep hygiene)  
- Receive personalized recommendations  
- Interact with an AI Sleep Coach through a chatbot interface  

---

## Features
- **Data Entry**: Upload screenshots & record memos  
- **AI Extraction**: GPT-4o extracts structured sleep data (JSON format)  
- **Visualization**: Charts for sleep patterns & hygiene over 7 days  
- **Recommendations**: AI-generated tips for better sleep habits  
- **Chatbot**: AI-powered conversational coaching  
- **Account Management**: User registration, login, and API key storage  

---

## Tech Stack
- **Frontend**: React / Next.js  
- **Backend**: Node.js (Express)  
- **Database**: PostgreSQL / MySQL / MongoDB (configurable)  
- **Authentication**: Custom login & registration  
- **AI Integration**: OpenAI GPT-4o  
- **File Handling**: Screenshots (images), voice memos (audio files)  

---

## Installation

### 1. Clone the repository
```bash
git clone https://github.com/your-username/sleep-pattern-app.git
cd sleep-pattern-app
```

### 2. Install dependencies
```bash
npm install
```

### 3. Setup environment variables
Create a `.env` file in the root with:
```env
OPENAI_API_KEY=your_openai_api_key
DATABASE_URL=your_database_connection_string
```

### 4. Run development server
```bash
npm run dev
```

The app should now be running at: `http://localhost:3000`

---

## Data Format
The app processes and stores sleep data in the following JSON format:

```json
[{
  "sleep_total": <number_of_seconds>,
  "sleep_rem": <number_of_seconds>,
  "sleep_deep": <number_of_seconds>,
  "had_late_meal": <true_or_false>,
  "read_before_bed": <true_or_false>,
  "used_screen_before_bed": <true_or_false>,
  "date": <date_in_ms>
}]
```

---

## Usage
1. Register and log in  
2. Configure your **OpenAI API key** in the **Account Page**  
3. Upload your **sleep screenshot** or **record a memo**  
4. View your **sleep analysis** and **charts**  
5. Click **“Give me recommendations”** to get AI-based tips  
6. Chat with the **Sleep Coach** for ongoing personalized guidance  

---

## Roadmap
- [ ] Deployment with Docker & Vercel  
- [ ] Notifications & reminders for daily sleep logging  
- [ ] Mobile-friendly PWA support  
- [ ] Multi-language support  

---

## License
This project is licensed under the MIT License.
